'use client';

import { getTranslation } from '@/lib/i18n';
import { Table, TableHeader, TableColumn, TableBody, TableCell, TableRow } from '@heroui/react'

export default function ConvertTimestampToDate({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const tableData = [
    {
      language: "Swift",
      code: `let timestamp = 1624713600.0
let date = NSDate(timeIntervalSince1970: timestamp)`
    },
    {
      language: "Go",
      code: `import (
    "time"
)
timestamp := int64(1624713600)
t := time.Unix(timestamp, 0)`
    },
    {
      language: "Java",
      code: `long timestamp = 1624713600;
java.util.Date date = new java.util.Date(timestamp * 1000);`
    },
    {
      language: "C",
      code: `#include <time.h>
time_t timestamp = 1624713600;
struct tm *timeinfo = localtime(&timestamp);`
    },
    {
      language: "JavaScript",
      code: `const timestamp = 1624713600;
const date = new Date(timestamp * 1000);`
    },
    {
      language: "Objective-C",
      code: `NSTimeInterval timestamp = 1624713600;
NSDate *date = [NSDate dateWithTimeIntervalSince1970:timestamp];`
    },
    {
      language: "MySQL",
      code: `SELECT from_unixtime(1624713600)`
    },
    {
      language: "SQLite",
      code: `SELECT datetime(1624713600, 'unixepoch')`
    },
    {
      language: "Erlang",
      code: `Timestamp = 1624713600,
Datetime = calendar:gregorian_seconds_to_datetime(Timestamp + 719528*24*3600).`
    },
    {
      language: "PHP",
      code: `<?php
// pure php
$timestamp = 1624713600;
$date = date('Y-m-d H:i:s', $timestamp);`
    },
    {
      language: "Python",
      code: `from datetime import datetime
timestamp = 1624713600
date = datetime.fromtimestamp(timestamp)`
    },
    {
      language: "Ruby",
      code: `timestamp = 1624713600
time = Time.at(timestamp)`
    },
    {
      language: "Shell",
      code: `date -d @1624713600
# macOS: date -r 1624713600`
    },
    {
      language: "Groovy",
      code: `long timestamp = 1624713600
Date date = new Date(timestamp * 1000)`
    },
    {
      language: "Lua",
      code: `timestamp = 1624713600
date = os.date("*t", timestamp)`
    },
    {
      language: ".NET/C#",
      code: `long timestamp = 1624713600;
DateTimeOffset date = DateTimeOffset.FromUnixTimeSeconds(timestamp);`
    },
    {
      language: "Dart",
      code: `int timestamp = 1624713600;
DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);`
    }
  ]

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('How to Convert Timestamp to Date in ...')}</h2>
      <Table hideHeader aria-label="Code examples for converting timestamp to date in different programming languages">
        <TableHeader>
          <TableColumn>Language</TableColumn>
          <TableColumn>Code</TableColumn>
        </TableHeader>
        <TableBody>
          {tableData.map((row) => (
            <TableRow key={row.language}>
              <TableCell className="font-medium">{row.language}</TableCell>
              <TableCell>
                <pre className="bg-gray-100 p-4 rounded-md">
                  <code>{row.code}</code>
                </pre>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  );
}
