'use client';
import { Accordion, AccordionItem } from "@heroui/react";
import { getTranslation } from '@/lib/i18n';

export default function DiscordFAQ({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const faq = [
    {
      label: "what-is-timestamp",
      question: t("What is a Discord timestamp?"),
      answer: t("A Discord timestamp is a native Discord feature that lets you insert a special code into a message. This code automatically displays in each user's local time zone, making it a powerful tool for coordinating events in international communities.")
    },
    {
      label: "how-to-use-generator",
      question: t("How do I use this Discord timestamp generator?"),
      answer: t("Using our Discord timestamp generator is incredibly simple: 1. Input a date and time using the picker. 2. Choose your preferred display format. 3. Click the 'Copy' button. 4. Paste the generated code into your Discord message.")
    },
    {
      label: "why-code-before-send",
      question: t("Why does my timestamp only show as code before I send it?"),
      answer: t("This is normal behavior. The Discord timestamp code (e.g., `<t:1786323810:R>`) will remain as code in your message box *before* you press send. It will only convert into the formatted time after the message is successfully posted to a channel.")
    },
    {
      label: "relative-time-support",
      question: t("Can I create a relative time like 'in 3 hours'?"),
      answer: t("Absolutely. That's exactly what the 'Relative Time' format (the `:R` in the code) is for. Our Discord timestamp generator makes it easy to create this dynamic, auto-updating timestamp, which is perfect for event countdowns.")
    },
    {
      label: "is-it-free",
      question: t("Is this Discord timestamp generator free to use?"),
      answer: t("Yes, completely free. Our tool is designed to be a convenient resource for all Discord users, helping you easily create and manage any Discord timestamp without any cost.")
    },
    {
      label: "supported-formats",
      question: t("What timestamp formats are available with this generator?"),
      answer: t("Our Discord timestamp generator supports all seven official formats provided by Discord. This includes short/long date, short/long time, a full short/long date and time combo, and the relative time format. You can preview all of them.")
    },
    {
      label: "what-is-unix-timestamp",
      question: t("What is a Unix timestamp and how does it relate to a Discord timestamp?"),
      answer: t("A Unix timestamp is the total number of seconds that have passed since 00:00:00 UTC on January 1, 1970. It's the technical foundation behind the entire Discord timestamp system. Our tool handles all these complex conversions for you.")
    },
    {
      label: "mobile-compatibility",
      question: t("Will the generated Discord timestamp work on the Discord mobile app?"),
      answer: t("Yes. The Discord timestamp is a cross-platform feature. As long as you paste the code correctly, it will display perfectly on the desktop client, web browser, and mobile apps.")
    },
    {
      label: "account-needed",
      question: t("Do I need an account to create a Discord timestamp?"),
      answer: t("Not at all. To provide the most convenient experience, our Discord timestamp generator requires no sign-up or login. You can open the webpage and start using it immediately.")
    },
    {
      label: "is-it-safe",
      question: t("Is it safe to use this Discord timestamp generator? Is my data logged?"),
      answer: t("It is completely safe. We prioritize user privacy. All date and time conversions for your Discord timestamp are performed locally in your browser. We never log, store, or transmit any data you enter.")
    },
    {
      label: "why-use-generator",
      question: t("Why should I use a generator instead of writing a timestamp manually?"),
      answer: t("While you can write the code by hand, the process is tedious and prone to errors. Using our Discord timestamp generator ensures you get a 100% accurate code every time, saving you valuable time and preventing the frustration of a broken Discord timestamp due to a small typo.")
    }
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('Frequently Asked Questions')}</h3>
      < Accordion
        selectionMode="multiple"
        className="border-foreground/10 border-[1px] rounded-2xl px-6"
      >
        {
          faq.map((item, index) => (
            <AccordionItem key={index} aria-label={item.label} title={item.question}>
              {item.answer}
            </AccordionItem>
          ))
        }
      </Accordion>
    </>
  )
}
