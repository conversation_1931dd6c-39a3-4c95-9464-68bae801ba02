import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardHeader, CardBody } from '@heroui/react';

export default function DiscordKeyFeatures({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const keyFeatures = [
    {
      title: t("Intuitive Date & Time Picker"),
      description: t("Forget about manually handling Unix time. Our user-friendly interface lets you visually select any date and time to create your perfect Discord timestamp instantly."),
    },
    {
      title: t("Complete Format Support"),
      description: t("We support all seven native styles, giving you full control over how your Discord timestamp appears. Find the ideal format for any event, announcement, or message."),
    },
    {
      title: t("Live Preview of Your Timestamp"),
      description: t("Our generator shows you exactly how your Discord timestamp will look in Discord before you copy it. This eliminates guesswork and ensures you always get the perfect result."),
    },
    {
      title: t("One-Click Code Copy"),
      description: t("Efficiency is everything. A single click is all it takes to copy the generated Discord timestamp code, ready to be pasted directly into your Discord client."),
    },
    {
      title: t("Fully Mobile-Responsive Design"),
      description: t("Need to create a timestamp on the go? Our Discord timestamp generator works flawlessly on any device—desktop, tablet, or phone—for a seamless experience everywhere."),
    },
    {
      title: t("Private and Secure Generation"),
      description: t("Your privacy is paramount when using our tool. Every Discord timestamp is generated client-side in your browser. We never see, collect, or store any data you enter."),
    },
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('Key Features of Our Discord Timestamp Generator')}</h3>
      < div className="flex flex-wrap gap-8 justify-between md:justify-start" >
        {
          keyFeatures.map((feature) => (
            <Card
              shadow="none"
              disableRipple
              className="select-none box-border border-foreground/10 border-[1px] min-w-[160px]  max-w-full md:max-w-[30%]  p-2 flex-shrink-0 bg-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-in-out"
              radius="lg"
              key={feature.title}
            >
              <CardHeader className="justify-between gap-5">
                <h2 className="text-lg font-bold px-2">{feature.title}</h2>
              </CardHeader>
              <CardBody>
                <p>{feature.description}</p>
              </CardBody>
            </Card>
          ))
        }
      </div>
    </>
  )
}
