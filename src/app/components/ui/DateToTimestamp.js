import { getTranslation } from '@/lib/i18n';
import { useState, useEffect } from 'react';
import { Button, Divider, Input, Select, SelectItem } from '@heroui/react';
import TimeZone from './TimeZone';
import InputWithCopy from './InputWithCopy';

export default function DateToTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const units = [
    { key: 'Seconds', label: t('Seconds') },
    { key: 'Milliseconds', label: t('Milliseconds') },
  ];

  // 格式化当前时间为 YYYY-MM-DD hh:mm:ss 格式
  const getCurrentDateTime = (locale = 'en', timezone = 'UTC') => {
    const date = new Date();
    const formatter2 = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date).replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$1-$2').replace(',', '');
    return formatter2;
  };

  const [datetime, setDatetime] = useState('');
  const [timezone, setTimezone] = useState('');
  const [unit, setUnit] = useState('Seconds');
  const [convertedTimestamp, setConvertedTimestamp] = useState('');

  // 设置默认时间为当前时间
  useEffect(() => {
    const currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const currentDateTime = getCurrentDateTime('en', currentTimezone);

    setTimezone(currentTimezone);
    setDatetime(currentDateTime);

    // 使用 setTimeout 确保状态更新后再执行转换
    setTimeout(() => {
      // 直接使用局部变量进行转换，避免依赖状态
      convertTimestampWithValues(currentDateTime, currentTimezone, unit);
    }, 0);
  }, []);

  // 通用转换函数，接受参数
  const convertTimestampWithValues = (dateTimeValue, timezoneValue, unitValue) => {
    if (!dateTimeValue || !timezoneValue) {
      setConvertedTimestamp('');
      return;
    }

    try {
      // 构造一个临时的Date对象
      const inputDate = new Date(dateTimeValue);

      // 检查日期是否有效
      if (isNaN(inputDate.getTime())) {
        setConvertedTimestamp('Invalid date');
        return;
      }

      // 获取本地时区偏移量（分钟）
      const localOffset = inputDate.getTimezoneOffset();

      // 获取目标时区在同一时间的偏移量
      // 创建一个在目标时区的格式化器
      const targetFormatter = new Intl.DateTimeFormat('en', {
        timeZone: timezoneValue,
        timeZoneName: 'longOffset'
      });

      const parts = targetFormatter.formatToParts(inputDate);
      const offsetPart = parts.find(part => part.type === 'timeZoneName');
      let targetOffsetMinutes = 0;

      if (offsetPart && offsetPart.value) {
        const offsetMatch = offsetPart.value.match(/GMT([+-])(\d{2}):(\d{2})/);
        if (offsetMatch) {
          const sign = offsetMatch[1] === '+' ? 1 : -1;
          const offsetHours = parseInt(offsetMatch[2]);
          const offsetMinutes = parseInt(offsetMatch[3]);
          targetOffsetMinutes = sign * (offsetHours * 60 + offsetMinutes);
        }
      }

      // 计算时间戳
      // 输入时间被视为目标时区的本地时间
      // 需要转换为UTC时间戳
      const utcTimestamp = inputDate.getTime() + (localOffset * 60 * 1000) - (targetOffsetMinutes * 60 * 1000);

      // 根据单位转换
      let result;
      if (unitValue === 'Seconds') {
        result = Math.floor(utcTimestamp / 1000).toString();
      } else {
        result = utcTimestamp.toString();
      }

      setConvertedTimestamp(result);
    } catch (error) {
      console.error('Error converting timestamp:', error);
      setConvertedTimestamp('Conversion error');
    }
  };

  // 按钮触发的转换函数，使用当前状态
  const convertTimestamp = () => {
    convertTimestampWithValues(datetime, timezone, unit);
  };

  return (
    <div className="section mt-4 border rounded-lg px-6 py-4">
      <h2 className="text-2xl font-bold mb-4">{t('Date to Timestamp')}</h2>
      <div className='flex gap-2 items-center flex-wrap'>
        <Input
          type="text"
          placeholder={t('YYYY-MM-DD hh:mm:ss')}
          value={datetime}
          onValueChange={setDatetime}
          className="w-[160px]"
          label={t('Date')}
        />
        <TimeZone
          locale={locale}
          value={timezone}
          onChange={setTimezone}
        />
        <Select
          selectedKeys={[unit]}
          onSelectionChange={(keys) => setUnit(Array.from(keys)[0])}
          className="w-[140px]"
          label={t('Unit')}
        >
          {units.map((unitOption) => (
            <SelectItem key={unitOption.key} value={unitOption.key}>
              {unitOption.label}
            </SelectItem>
          ))}
        </Select>
        <Button onPress={convertTimestamp} color="primary">
          {t('Convert')}
        </Button>
      </div>
      <Divider className="my-4"></Divider>
      <InputWithCopy locale={locale} text={convertedTimestamp}></InputWithCopy>
    </div>
  );
}
