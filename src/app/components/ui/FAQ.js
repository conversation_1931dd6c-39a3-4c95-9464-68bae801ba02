'use client';
import { Accordion, AccordionItem } from "@heroui/react";
import { getTranslation } from '@/lib/i18n';

export default function FAQ({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const faq = [
    {
      label: "What is a Unix timestamp?",
      question: t("What is a Unix timestamp?"),
      answer: t("A Unix timestamp is the number of seconds (or milliseconds) since January 1, 1970, 00:00:00 UTC, known as the Unix Epoch. Our Unix timestamp converter helps you work with this format effortlessly.")
    },
    {
      label: "What does a Unix timestamp converter do?",
      question: t("What does a Unix timestamp converter do?"),
      answer: t("A Unix timestamp converter transforms time data between numerical Unix timestamps and human-readable dates. It supports both timestamp to date and date to timestamp conversions.")
    },
    {
      label: "How do I convert a timestamp to a date?",
      question: t("How do I convert a timestamp to a date?"),
      answer: t("Enter the timestamp in our Unix timestamp converter, select the unit (seconds or milliseconds), choose a time zone, and get the date in formats like YYYY-MM-DD hh:mm:ss or MM/DD/YYYY hh:mm:ss.")
    },
    {
      label: "How do I convert a date to a timestamp?",
      question: t("How do I convert a date to a timestamp?"),
      answer: t("Input a date in our Unix timestamp converter, select the time zone and unit (seconds or milliseconds), and instantly convert date to timestamp with a single click.")
    },
    {
      label: "Can I copy the converted results?",
      question: t("Can I copy the converted results?"),
      answer: t("Yes! Our Unix timestamp converter includes a copy feature for both timestamp to date and date to timestamp results, making it easy to use in your projects.")
    },
    {
      label: "Does the tool support different time zones?",
      question: t("Does the tool support different time zones?"),
      answer: t("Absolutely. Our Unix timestamp converter supports multiple time zones, ensuring accurate convert timestamp to date and convert date to timestamp results worldwide.")
    },
    {
      label: "What date formats are available?",
      question: t("What date formats are available?"),
      answer: t("You can convert timestamp to date in multiple formats, including default, YYYY-MM-DD hh:mm:ss, and MM/DD/YYYY hh:mm:ss, customizable to your needs.")
    },
    {
      label: "Is the Unix timestamp converter free?",
      question: t("Is the Unix timestamp converter free?"),
      answer: t("Yes, our Unix timestamp converter is completely free, offering unlimited timestamp to date and date to timestamp conversions with a user-friendly interface.")
    },
    {
      label: "What is the 2038 problem?",
      question: t("What is the 2038 problem?"),
      answer: t("The 2038 problem occurs when 32-bit systems can't handle timestamps after January 19, 2038. Our Unix timestamp converter uses 64-bit support to avoid this issue.")
    },
    {
      label: "Can I get the current Unix timestamp?",
      question: t("Can I get the current Unix timestamp?"),
      answer: t("Yes, our tool displays the current Unix timestamp in seconds or milliseconds, with options to pause/resume refreshing and copy the value instantly.")
    },
    {
      label: "Why would I need to convert timestamp to date?",
      question: t("Why would I need to convert timestamp to date?"),
      answer: t("Converting timestamp to date is useful for debugging logs, displaying dates in apps, or generating reports, and our Unix timestamp converter makes it quick and accurate.")
    },
    {
      label: "Who can benefit from a Unix timestamp converter?",
      question: t("Who can benefit from a Unix timestamp converter?"),
      answer: t("Developers, data analysts, and system administrators use our Unix timestamp converter for tasks like API integration, log analysis, and convert date to timestamp for databases.")
    }
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('Frequently Asked Questions about Unix Timestamp Converter')}</h3>
      < Accordion
        selectionMode="multiple"
        className="border-foreground/10 border-[1px] rounded-2xl px-6"
      >
        {
          faq.map((item, index) => (
            <AccordionItem key={index} aria-label={item.label} title={item.question}>
              {item.answer}
            </AccordionItem>
          ))
        }
      </Accordion>
    </>
  )
}
