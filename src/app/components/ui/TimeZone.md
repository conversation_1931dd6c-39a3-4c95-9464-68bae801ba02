# TimeZone 组件

一个功能完整、可复用的时区选择组件，基于 HeroUI Select 组件构建。

## 功能特性

- ✅ **完整时区支持**: 支持所有 IANA 时区标识符（400+ 时区）
- ✅ **UTC偏移量显示**: 自动显示每个时区的当前UTC偏移量
- ✅ **浏览器时区检测**: 自动检测并标记用户的浏览器时区
- ✅ **多种排序方式**: 支持按字母或UTC偏移量排序
- ✅ **向后兼容**: 在不支持的浏览器中回退到常用时区
- ✅ **国际化支持**: 完整的多语言支持
- ✅ **无障碍访问**: 符合 WCAG 标准
- ✅ **高度可定制**: 丰富的配置选项

## 基本用法

```javascript
import TimeZone from './TimeZone';

function MyComponent() {
  const [selectedTimezone, setSelectedTimezone] = useState('');

  return (
    <TimeZone
      locale="en"
      value={selectedTimezone}
      onChange={setSelectedTimezone}
    />
  );
}
```

## API 参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `locale` | `string` | `'en'` | 语言环境 |
| `value` | `string` | - | 当前选中的时区值 |
| `onChange` | `function` | - | 时区变化回调函数 |
| `className` | `string` | `'w-[340px]'` | 自定义样式类名 |
| `label` | `string` | - | 标签文本，不提供则使用默认翻译 |
| `showBrowserDefault` | `boolean` | `true` | 是否显示浏览器默认时区标记 |
| `sortByOffset` | `boolean` | `false` | 是否按UTC偏移量排序 |
| `commonTimezones` | `Array` | - | 常用时区列表，用于回退 |
| `isDisabled` | `boolean` | `false` | 是否禁用 |
| `placeholder` | `string` | - | 占位符文本 |

## 使用示例

### 1. 基本使用
```javascript
<TimeZone
  locale="en"
  value={timezone}
  onChange={setTimezone}
/>
```

### 2. 自定义样式和标签
```javascript
<TimeZone
  locale="zh-CN"
  value={timezone}
  onChange={setTimezone}
  className="w-[400px]"
  label="选择时区"
  placeholder="请选择时区"
/>
```

### 3. 按UTC偏移量排序
```javascript
<TimeZone
  locale="en"
  value={timezone}
  onChange={setTimezone}
  sortByOffset={true}
  showBrowserDefault={false}
/>
```

### 4. 自定义常用时区（用于回退）
```javascript
<TimeZone
  locale="en"
  value={timezone}
  onChange={setTimezone}
  commonTimezones={[
    'UTC',
    'Asia/Shanghai',
    'America/New_York',
    'Europe/London'
  ]}
/>
```

## 显示格式

时区选项显示格式为：`时区名称 (UTC偏移量)`

示例：
- `Asia/Shanghai (GMT+08:00) - Browser Default`
- `America/New_York (GMT-05:00)`
- `Europe/London (GMT+00:00)`
- `UTC (GMT+00:00)`

## 国际化

组件支持多语言，需要在翻译文件中添加以下键：

```json
{
  "Timezone": "Timezone",
  "Browser Default": "Browser Default",
  "Select timezone": "Select timezone"
}
```

## 注意事项

1. **客户端渲染**: 组件使用 `'use client'` 指令，确保在客户端环境中运行
2. **浏览器兼容性**: 在不支持 `Intl.supportedValuesOf` 的浏览器中会回退到常用时区列表
3. **性能优化**: 使用 `useMemo` 缓存时区列表，避免重复计算
4. **自动初始化**: 如果没有传入 `value` 且提供了 `onChange`，会自动设置为浏览器时区

## 依赖

- `@heroui/react` - UI 组件库
- `@/lib/i18n` - 国际化工具
- `React` - useState, useEffect, useMemo hooks
