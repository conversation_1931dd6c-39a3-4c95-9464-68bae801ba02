'use client';

import { getTranslation } from '@/lib/i18n';
import { DatePicker } from "@heroui/react";
import { now, getLocalTimeZone } from "@internationalized/date";
import InputWithCopy from './InputWithCopy';
import { useState, useEffect } from 'react';

export default function DiscordTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [unixTimestamp, setUnixTimestamp] = useState('');

  const calculateTimestampFormats = (date) => {
    const unixTimestamp = Math.floor(date.getTime() / 1000);
    setUnixTimestamp(unixTimestamp);

    const shortTime = new Intl.DateTimeFormat(locale, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);

    const longTime = new Intl.DateTimeFormat(locale, {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);

    const shortDate = new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date);

    const longDate = new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);

    const shortDateTime = new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);

    const longDateTime = new Intl.DateTimeFormat(locale, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);

    const relativeTime = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' }).format(
      Math.floor((date.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)),
      'day'
    );

    return [
      {
        title: t("Short Time"),
        description: shortTime,
        format: `<t:${unixTimestamp}:t>`
      },
      {
        title: t("Long Time"),
        description: longTime,
        format: `<t:${unixTimestamp}:T>`
      },
      {
        title: t("Short Date"),
        description: shortDate,
        format: `<t:${unixTimestamp}:d>`
      },
      {
        title: t("Long Date"),
        description: longDate,
        format: `<t:${unixTimestamp}:D>`
      },
      {
        title: t("Short Date/Time"),
        description: shortDateTime,
        format: `<t:${unixTimestamp}:f>`
      },
      {
        title: t("Long Date/Time"),
        description: longDateTime,
        format: `<t:${unixTimestamp}:F>`
      },
      {
        title: t("RelativeTime"),
        description: relativeTime,
        format: `<t:${unixTimestamp}:R>`
      }
    ];
  };

  const [timestampFormats, setTimestampFormats] = useState([]);

  // Client-side hydration fix - only calculate timestamps on client
  useEffect(() => {
    const currentDate = new Date();
    setTimestampFormats(calculateTimestampFormats(currentDate));
  }, []);

  // Handle DatePicker change
  const handleDateChange = (date) => {
    if (date) {
      const jsDate = new Date(date.year, date.month - 1, date.day, date.hour || 0, date.minute || 0, date.second || 0);
      setTimestampFormats(calculateTimestampFormats(jsDate));
    }
  };

  return (
    <div className='border rounded-lg px-6 py-2'>
      <DatePicker
        hideTimeZone
        showMonthAndYearPickers
        defaultValue={now(getLocalTimeZone())}
        variant="bordered"
        className='w-[240px] my-2'
        onChange={handleDateChange}
        label={t('Select Date and time')}
      />
      <h3 className="text-lg font-bold mb-2">{t("Timestamp Formats")}</h3>
      <div className='flex flex-col gap-2 flex-wrap mb-2'>
        {timestampFormats.map((format) => (
          <div key={format.title} className="flex gap-2 border-b pb-2 items-center justify-between">
            <div className="flex flex-col gap-1 lg:w-96">
              <span className="text-sm sm:text-lg font-bold">{format.title}</span>
              <span className="text-sm text-gray-500">{format.description}</span>
            </div>
            <InputWithCopy locale={locale} text={format.format}></InputWithCopy>
          </div>
        ))}
      </div>
      <div className="flex gap-2 items-center">
        <span>{t("Unix Timestamp")}: </span>
        <code className="bg-gray-100 dark:bg-gray-900 p-2 rounded-md">{unixTimestamp}</code>
      </div>
    </div>
  );
}
