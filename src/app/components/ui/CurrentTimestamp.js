'use client';
import { getTranslation } from '@/lib/i18n';
import { useState, useEffect, useRef } from 'react';
import { Button } from "@heroui/react";
export default function CurrentTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [timestamp, setTimestamp] = useState(0);
  const [unit, setUnit] = useState('Seconds');
  const [isRunning, setIsRunning] = useState(true);
  const intervalRef = useRef(null);

  // 获取当前时间戳，根据单位返回相应值
  const getCurrentTimestamp = () => {
    const now = Date.now();
    return unit === 'Seconds' ? Math.floor(now / 1000) : now;
  };

  // 客户端初始化
  useEffect(() => {
    setTimestamp(getCurrentTimestamp());
  }, []);

  // 定时器效果
  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTimestamp(getCurrentTimestamp());
      }, 100); // 每100ms更新一次
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, unit]);

  // 单位切换时更新时间戳
  useEffect(() => {
    setTimestamp(getCurrentTimestamp());
  }, [unit]);

  // 切换单位
  const toggleUnit = () => {
    const newUnit = unit === 'Seconds' ? 'Milliseconds' : 'Seconds';
    setUnit(newUnit);
  };

  // 开始/停止定时器
  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(timestamp.toString());
      // 可以添加一个提示，但这里先简单实现
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  return (
    <div className="border rounded-lg px-6 py-2">
      <h2 className='text-2xl font-bold mb-4'>{t('Current Unix Timestamp')}</h2>
      <div className="flex items-end gap-2 mb-4">
        <p className='text-xl'>{timestamp}</p>
        <span className='text-sm text-gray-500'>{t(unit)}</span>
      </div>
      <div className='flex items-center gap-2'>
        <Button onPress={toggleUnit}>{t('s ⇌ ms')}</Button>
        <Button onPress={copyToClipboard}>{t('Copy')}</Button>
        {isRunning ? (
          <Button onPress={toggleTimer} color='danger'>{t('Stop')}</Button>
        ) : (
          <Button onPress={toggleTimer}>{t('Start')}</Button>
        )}
      </div>
    </div>
  )
}
