import { Link } from '@heroui/react';
export default function FeaturedOn() {
  return (
    <div className='flex items-center flex-wrap gap-1'>
      <Link href="https://aistage.net" className="text-sm hover:text-primary" target="_blank">AIStage</Link>
      <span className="mx-2">|</span>
      <Link href="https://startupfa.me/s/timestamps.top?utm_source=timestamps.top" target="_blank"><img src="https://startupfa.me/badges/featured/default.webp" alt="Featured on Startup Fame" className='h-[36px] w-auto' /></Link>
      <span className="mx-2">|</span>
      <Link href="https://magicbox.tools" target="_blank">
        <img src="https://magicbox.tools/badge.svg" alt="Featured on MagicBox.tools" className='h-[36px] w-auto' />
      </Link>
      <span className="mx-2">|</span>
      <Link href="https://kontext-ai.com/">Kontext AI</Link>
      <span className="mx-2">|</span>
      <Link href="https://turbo0.com/item/unix-timestamp-converter" target="_blank" rel="noopener noreferrer">
        <img src="https://img.turbo0.com/badge-listed-light.svg" alt="Listed on Turbo0" className='h-[36px] w-auto' />
      </Link>
      <span className="mx-2">|</span>
      <Link href='https://submitaitools.org/'>Submit AI Tools</Link>
      <span className="mx-2">|</span>
      <Link href="https://twelve.tools" target="_blank"><img src="https://twelve.tools/badge3-light.svg" alt="Featured on Twelve Tools" className='h-[36px] w-auto' /></Link>
      <span className="mx-2">|</span>
      <Link title="ai tools code.market" href="https://code.market?code.market=verified">
        <img alt="ai tools code.market" title="ai tools code.market" src="https://code.market/assets/manage-product/featured-logo-bright.svg" className='h-[36px] w-auto' />
      </Link>
      <span className="mx-2">|</span>
      <Link href="https://www.toolpilot.ai/products/brat-gen" target="_blank"><img src="https://www.toolpilot.ai/cdn/shop/files/toolpilot-badge-w.png" alt="Unix Timestamp Converter Is Featured On ToolPilot.ai" className='h-[36px] w-auto' /></Link>
      <span className="mx-2">|</span>
      <Link href="https://startuplist.ing/p/ph5e2d?utm_source=timestamps.top/" target="_blank" rel="noopener">
        <img src="https://startuplist.ing/badges/light-normal.svg" alt="Featured on StartupList.ing" className='h-[36px] w-auto' />
      </Link>
      <span className="mx-2">|</span>
      <Link href="https://starterbest.com" target="_blank" rel="noopener noreferrer">
        <img src="https://starterbest.com/badages-awards.svg"
          alt="Featured on Starter Best" className='h-[36px] w-auto' />
      </Link>
      <span className="mx-2">|</span>
      <Link href="https://indie.deals?ref=https%3A%2F%2Ftimestamps.top" target="_blank" rel="noopener noreferrer" className='text-sm hover:text-primary'>Find us on Indie.Deals</Link>
      <span className="mx-2">|</span>
    </div>
  )
}
