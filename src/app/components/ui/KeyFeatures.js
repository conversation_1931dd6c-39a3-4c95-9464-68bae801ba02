import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardHeader, CardBody } from '@heroui/react';

export default function KeyFeatures({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const keyFeatures = [
    {
      title: t("Get Current Unix Timestamp"),
      description: t("Instantly retrieve the current Unix timestamp in seconds or milliseconds, with options to pause/resume refreshing and copy the timestamp for quick use."),
    },
    {
      title: t("Convert Timestamp to Date"),
      description: t("Effortlessly convert timestamp to date by selecting your preferred unit (seconds or milliseconds) and time zone, with results in multiple formats like YYYY-MM-DD hh:mm:ss or MM/DD/YYYY hh:mm:ss."),
    },
    {
      title: t("Convert Date to Timestamp"),
      description: t("Seamlessly convert date to timestamp by inputting a date, choosing a time zone, and selecting seconds or milliseconds, with a one-click copy feature."),
    },
    {
      title: t("Flexible Time Zone Support"),
      description: t("Our Unix timestamp converter supports multiple time zones, ensuring accurate timestamp to date and date to timestamp conversions worldwide."),
    },
    {
      title: t("Multiple Date Formats"),
      description: t("Choose from various date formats, including default, YYYY-MM-DD hh:mm:ss, and MM/DD/YYYY hh:mm:ss, for precise convert timestamp to date results."),
    },
    {
      title: t("Free and User-Friendly"),
      description: t("Enjoy a completely free Unix timestamp converter with an intuitive interface, perfect for developers and analysts needing fast timestamp to date or date to timestamp conversions."),
    },
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('Key Features of Our Unix Timestamp Converter')}</h3>
      < div className="flex flex-wrap gap-8 justify-between md:justify-start" >
        {
          keyFeatures.map((feature) => (
            <Card
              shadow="none"
              disableRipple
              className="select-none box-border border-foreground/10 border-[1px] min-w-[160px]  max-w-full md:max-w-[30%]  p-2 flex-shrink-0 bg-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-in-out"
              radius="lg"
              key={feature.title}
            >
              <CardHeader className="justify-between gap-5">
                <h2 className="text-lg font-bold px-2">{feature.title}</h2>
              </CardHeader>
              <CardBody>
                <p>{feature.description}</p>
              </CardBody>
            </Card>
          ))
        }
      </div>
    </>
  )
}
