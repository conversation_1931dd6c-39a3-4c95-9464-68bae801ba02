'use client';
import { Select, SelectItem } from '@heroui/react';
import { getTranslation } from '@/lib/i18n';
import { useState, useEffect, useMemo } from 'react';

/**
 * TimeZone 选择组件
 * @param {Object} props
 * @param {string} props.locale - 语言环境，默认 'en'
 * @param {string} props.value - 当前选中的时区值
 * @param {Function} props.onChange - 时区变化回调函数
 * @param {string} props.className - 自定义样式类名
 * @param {string} props.label - 标签文本，如果不提供则使用默认翻译
 * @param {boolean} props.showBrowserDefault - 是否显示浏览器默认时区标记，默认 true
 * @param {boolean} props.sortByOffset - 是否按UTC偏移量排序，默认 false（按字母排序）
 * @param {Array} props.commonTimezones - 常用时区列表，用于回退
 * @param {boolean} props.isDisabled - 是否禁用
 * @param {string} props.placeholder - 占位符文本
 */
export default function TimeZone({
  locale = 'en',
  value,
  onChange,
  className = "w-[340px]",
  label,
  showBrowserDefault = true,
  sortByOffset = false,
  commonTimezones,
  isDisabled = false,
  placeholder,
  ...props
}) {
  const t = (key) => getTranslation(locale, key);

  const [isClient, setIsClient] = useState(false);
  const [browserTimezone, setBrowserTimezone] = useState('UTC');

  // 客户端初始化
  useEffect(() => {
    setIsClient(true);
    const detected = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setBrowserTimezone(detected);

    // 如果没有传入value且需要自动设置为浏览器时区
    if (!value && onChange) {
      onChange(detected);
    }
  }, []);

  // 获取时区的UTC偏移量
  const getTimezoneOffset = (timezoneName) => {
    try {
      const formatter = new Intl.DateTimeFormat('en', {
        timeZone: timezoneName,
        timeZoneName: 'longOffset'
      });
      const parts = formatter.formatToParts(new Date());
      const offsetPart = parts.find(part => part.type === 'timeZoneName');
      return offsetPart ? offsetPart.value : 'GMT+00:00';
    } catch (error) {
      return 'GMT+00:00';
    }
  };

  // 获取时区的数字偏移量（用于排序）
  const getNumericOffset = (timezoneName) => {
    try {
      const offset = getTimezoneOffset(timezoneName);
      const match = offset.match(/GMT([+-])(\d{2}):(\d{2})/);
      if (match) {
        const sign = match[1] === '+' ? 1 : -1;
        const hours = parseInt(match[2]);
        const minutes = parseInt(match[3]);
        return sign * (hours * 60 + minutes);
      }
      return 0;
    } catch (error) {
      return 0;
    }
  };

  // 默认常用时区
  const defaultCommonTimezones = [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Asia/Kolkata',
    'Australia/Sydney',
  ];

  // 时区选项列表
  const timezoneOptions = useMemo(() => {
    if (!isClient) {
      return [{ key: 'UTC', label: 'UTC (GMT+00:00)' }];
    }

    try {
      // 获取所有支持的时区
      const allTimezones = Intl.supportedValuesOf('timeZone');

      // 转换为选项格式
      const options = allTimezones.map(tz => {
        const offset = getTimezoneOffset(tz);
        const label = `${tz} (${offset})`;

        return {
          key: tz,
          label: label,
          offset: getNumericOffset(tz)
        };
      });

      // 排序
      if (sortByOffset) {
        // 按UTC偏移量排序
        options.sort((a, b) => {
          if (showBrowserDefault && a.key === browserTimezone) return -1;
          if (showBrowserDefault && b.key === browserTimezone) return 1;
          return a.offset - b.offset;
        });
      } else {
        // 按字母排序
        options.sort((a, b) => {
          if (showBrowserDefault && a.key === browserTimezone) return -1;
          if (showBrowserDefault && b.key === browserTimezone) return 1;
          return a.key.localeCompare(b.key);
        });
      }

      return options;
    } catch (error) {
      // 回退到常用时区
      console.warn('Intl.supportedValuesOf not supported, using common timezones');
      const fallbackTimezones = commonTimezones || defaultCommonTimezones;

      const options = fallbackTimezones.map(tz => {
        const offset = getTimezoneOffset(tz);
        const isBrowserDefault = showBrowserDefault && tz === browserTimezone;
        const label = isBrowserDefault
          ? `${tz} (${offset}) - ${t('Browser Default')}`
          : `${tz} (${offset})`;

        return {
          key: tz,
          label: label,
          offset: getNumericOffset(tz)
        };
      });

      // 确保浏览器时区在列表中
      if (showBrowserDefault && !options.find(opt => opt.key === browserTimezone)) {
        const offset = getTimezoneOffset(browserTimezone);
        options.unshift({
          key: browserTimezone,
          label: `${browserTimezone} (${offset}) - ${t('Browser Default')}`,
          offset: getNumericOffset(browserTimezone)
        });
      }

      return options;
    }
  }, [isClient, browserTimezone, showBrowserDefault, sortByOffset, commonTimezones, t]);

  const handleSelectionChange = (keys) => {
    const selectedTimezone = Array.from(keys)[0];
    if (onChange) {
      onChange(selectedTimezone);
    }
  };

  return (
    <Select
      selectedKeys={value ? [value] : []}
      onSelectionChange={handleSelectionChange}
      className={className}
      label={label || t('Timezone')}
      placeholder={placeholder || t('Select timezone')}
      isDisabled={isDisabled}
      aria-label={label || t('Timezone')}
      {...props}
    >
      {timezoneOptions.map((tz) => (
        <SelectItem key={tz.key} value={tz.key}>
          {tz.label}
        </SelectItem>
      ))}
    </Select>
  );
}
