import { getTranslation } from '@/lib/i18n';
import { Card, CardBody } from '@heroui/react';

export default function UserFeedback({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const userFeedbacks = [
    {
      name: '<PERSON>',
      title: t('Backend Developer'),
      description: t('This Unix timestamp converter is a lifesaver for debugging server logs. I can convert timestamp to date in seconds or milliseconds with accurate time zone support, and the copy feature is so convenient!'),
    },
    {
      name: '<PERSON>',
      title: t('Data Analyst'),
      description: t('As a data analyst, I use this tool to convert date to timestamp for database queries. The ability to pause the current timestamp and choose formats like YYYY-MM-DD hh:mm:ss is fantastic!'),
    },
    {
      name: '<PERSON><PERSON>',
      title: t('Frontend Developer'),
      description: t("The best free Unix timestamp converter I've found! Converting timestamp to date across different time zones for my app's user interface is fast and reliable."),
    },
    {
      name: '<PERSON>',
      title: t('API Developer'),
      description: t('This tool simplifies my workflow when I need to convert timestamp to date for API integrations. The multiple format options and time zone support are perfect for my projects.'),
    },
    {
      name: '<PERSON>',
      title: t('System Administrator'),
      description: t('I rely on this Unix timestamp converter to convert date to timestamp for scheduling tasks. The intuitive interface and one-click copy feature make my job so much easier.'),
    },
    {
      name: 'Liam Patel',
      title: t('Business Intelligence Analyst'),
      description: t("For generating reports, this tool's timestamp to date conversion is a must-have. Switching between units and copying timestamps or dates in my preferred format is seamless!"),
    },
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('User Reviews of Our Unix Timestamp Converter')}</h3>
      <div className="flex flex-wrap gap-8 justify-between">
        {userFeedbacks.map((feedback, index) => (
          <Card
            shadow="none"
            disableRipple
            className="select-none box-border border-foreground/10 border-[1px] min-w-[160px] max-w-full md:max-w-[30%] p-2 flex-shrink-0"
            radius="lg"
            key={index}
          >
            <CardBody>
              <p>{feedback.description}</p>
              <p className="text-sm mt-8">{feedback.name}</p>
              <p className="text-sm text-gray-500 mt-2">{feedback.title}</p>
            </CardBody>
          </Card>
        ))}
      </div>
    </>
  )
}
