import { getTranslation } from '@/lib/i18n';
import FAQ from '@/app/components/ui/FAQ';
import Hero from '@/app/components/ui/Hero';
import KeyFeatures from '@/app/components/ui/KeyFeatures';
import UserFeedback from '@/app/components/ui/UserFeedback';
import FeaturedOn from '@/app/components/ui/FeaturedOn';
import GetCurrentTimestamp from '../components/ui/GetCurrentTimestamp';
import ConvertTimestampToDate from '../components/ui/ConvertTimestampToDate';
import ConvertDateToTimestamp from '../components/ui/ConvertDateToTimestamp';

export default async function Home({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <div className="page-container">
        <div className="section">
          <Hero locale={locale} />
        </div>
        <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-10">
          <h2 className="text-2xl font-bold px-2 py-4">{t('What is Unix Timestamp Converter')}</h2>
          <div className="px-2">
            <p className='mb-2'>
              {t('A Unix timestamp converter is a vital tool for managing time data in programming and data analysis. A Unix timestamp is the number of seconds since January 1, 1970, 00:00:00 UTC, known as the Unix Epoch. This compact numerical format is widely used in databases, APIs, and systems for its simplicity and compatibility.')}
            </p>
            <p className='mb-2'>{t('With our Unix timestamp converter, you can easily perform timestamp to date conversions (e.g., 1697059200 to "October 12, 2023, 00:00:00 UTC") and date to timestamp conversions (e.g., "October 12, 2023" to 1697059200). These features are perfect for developers working on user interfaces, debugging logs, or integrating APIs with different time formats.')}</p>
            <p className='mb-2'>{t('The 2038 problem affects older 32-bit systems, where timestamps may overflow after January 19, 2038. Modern 64-bit systems and our Unix timestamp converter handle this seamlessly.')}</p>
            <p>{t("Whether you're managing time zones or formatting dates, our Unix timestamp converter offers a fast, reliable solution for all your timestamp to date and date to timestamp needs.")}</p>
          </div>
        </div>
        <div className="section mt-10">
          <GetCurrentTimestamp locale={locale} />
        </div>
        <div className="section mt-10">
          <ConvertTimestampToDate locale={locale} />
        </div>
        <div className="section mt-10">
          <ConvertDateToTimestamp locale={locale} />
        </div>
        <div className="section mt-10">
          <KeyFeatures locale={locale} />
        </div>
        <div className="section mt-10">
          <UserFeedback locale={locale} />
        </div>
        <div className="section">
          <FAQ locale={locale} />
        </div>
        <div className="section">
          <FeaturedOn />
        </div>
      </div>
    </>
  );
}
