import DiscordTimestamp from '@/app/components/ui/DiscordTimestamp';
import { getTranslation } from '@/lib/i18n';
import DiscordKeyFeatures from '@/app/components/ui/DiscordKeyFeatures';
import DiscordFAQ from '@/app/components/ui/DiscordFAQ';

export async function generateMetadata({ params }) {
  const title = 'Free Discord Timestamp Generator';
  const description = "Never confuse time zones again! Our free Discord timestamp generator creates perfect Discord timestamp codes. Supports all 7 formats with a live preview.";
  const image = `${process.env.NEXT_PUBLIC_SITE_URL}/images/og.png`;

  return {
    title: title,
    description: description,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/discord-timestamp`,
    },
    openGraph: {
      title: title,
      description: description,
      type: 'website',
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/discord-timestamp`,
      siteName: 'Discord Timestamp Generator',
      images: [{
        url: image
      }]
    },
    twitter: {
      card: 'summary_large_image',
      site: '@Discord Timestamp Generator',
      title: title,
      description: description,
      images: [image]
    },
  }
}

export default function DiscordTimestampPage({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <div className="page-container">
      <div className="section text-center pt-10 pb-2">
        <h1 className="text-5xl font-bold text-primary mb-2">{t("Discord Timestamp Generator")}</h1>
      </div>
      <div className='section mt-2 md:px-40'>
        <DiscordTimestamp locale={locale} />
      </div>
      <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-10 px-4">
        <h2 className='text-2xl font-bold px-2 py-4'>{t("What is a Discord Timestamp and Why is it Essential?")}</h2>
        <h3 className='text-xl font-bold my-2'>{t("What Is a Discord Timestamp?")}</h3>
        <p> {t("Have you ever struggled with managing different time zones in an international community? A Discord timestamp is the perfect solution to this exact problem. Simply put, it's a special code that displays a dynamically adjusted time within a Discord message. When you send a message containing this timestamp, it automatically converts to the local time for everyone who sees it.")}</p>
        <h3 className='text-xl font-bold my-2'>{t("Why Is a Discord Timestamp So Important?")}</h3>
        <p>{t("A simple Discord timestamp can dramatically improve communication efficiency and user experience. Its importance is highlighted by these key benefits:")}</p>
        <h4 className='text-lg font-bold my-2'>{t("1. Seamless Coordination Across Time Zones")}</h4>
        <p className='pl-4'>{t("In any community with international members, time zone conversion is the biggest pain point. A Discord timestamp automatically displays the correct local time for every user, completely eliminating the confusion and guesswork caused by time differences. This makes global collaboration easier than ever before.")}</p>
        <h4 className='text-lg font-bold my-2'>{t("2. Enhanced Clarity and Authority for Announcements")}</h4>
        <p className='pl-4'>{t("Compared to a vague phrase like \"8 PM tonight, \" a dynamic timestamp that is precise to the minute appears far more professional and credible. This not only adds authority to your events and announcements but also ensures that your information is conveyed accurately, preventing members from asking repetitive questions.")}</p>
        <h4 className='text-lg font-bold my-2'>{t("3. Elimination of Misunderstandings and Communication Overhead")}</h4>
        <p className='pl-4'> {t("An incorrect event time can cause members to miss critical content. By using a Discord timestamp, you can eliminate problems caused by time-related misunderstandings right from the source. When everyone sees one, unified, and correct time, you as the organizer no longer need to spend extra energy on repeated explanations and confirmations.")}</p>
      </div>
      <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-10 px-4">
        <h2 className='text-2xl font-bold px-2 py-4'>{t("How to Use Our Discord Timestamp Generator")}</h2>
        <p>{t("With our Discord timestamp generator, you don't need to know anything about complex Unix time. Just follow these simple steps to create the perfect Discord timestamp in seconds.")}</p>
        <h3 className='text-xl font-bold my-2'>{t("1. Enter Your Date and Time")}</h3>
        <p className='pl-4'>{t("At the top of the page, use our intuitive date and time picker to input the exact moment you want to share. You can be precise down to the minute to ensure your event time is accurate.")}</p>
        <h3 className='text-xl font-bold my-2'>{t("2. Choose Your Preferred Display Format")}</h3>
        <p className='pl-4'>{t("A powerful Discord timestamp can have various appearances. You can choose from a detailed format that includes the full date and time to a concise format showing only relative time (e.g., \" in 2 hours\"). Our tool shows you a live preview of how each format will look.")}</p>
        <h3 className='text-xl font-bold my-2'>{t("3. Generate and Copy the Timestamp Code with One Click")}</h3>
        <p className='pl-4'>{t("After selecting a format, our Discord timestamp generator instantly provides the corresponding code (e.g., <t:1759987200:F>). Simply click the \"Copy\" button, and the code will be saved to your clipboard automatically.")}</p>
        <h3 className='text-xl font-bold my-2'>{t("4. Paste the Code into Discord")}</h3>
        <p className='pl-4'>{t("Return to your Discord client and paste the copied code into a chat box, event announcement, or anywhere you want the time to appear. Please note, it will look like code in your message box before you hit send. Once the message is sent, that Discord timestamp will magically transform into a clear, localized time for everyone to see!")}</p>
      </div>
      <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-10 px-4">
        <h2 className='text-2xl font-bold px-2 py-4'>{t("Discord Timestamp Formats")}</h2>
        <p>{t("Discord uses the <t:timestamp:format> syntax, supporting seven formats:")}</p>
        <ul className='list-disc pl-6 space-y-2 my-4'>
          <li>{t("t: Short time (e.g., 4:20 PM)")}</li>
          <li>{t("T: Long time (e.g., 4:20:30 PM)")}</li>
          <li>{t("d: Short date (e.g., 04/20/2024)")}</li>
          <li>{t("D: Long date (e.g., April 20, 2024)")}</li>
          <li>{t("f: Short date/time (e.g., April 20, 2024 4:20 PM)")}</li>
          <li>{t("F: Long date/time (e.g., Saturday, April 20, 2024 4:20 PM)")}</li>
          <li>{t("R: Relative time (e.g., 2 months ago, 3 days from now)")}</li>
        </ul>
      </div>
      <div className="section mt-10">
        <DiscordKeyFeatures locale={locale} />
      </div>
      <div className="section mt-10">
        <DiscordFAQ locale={locale} />
      </div>
    </div>
  );
}
