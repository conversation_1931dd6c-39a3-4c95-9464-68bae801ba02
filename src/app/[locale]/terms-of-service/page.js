export async function generateMetadata({ params }) {
  const title = 'Terms of Service';
  const description = 'Terms of Service for Brat-Gen.';

  return {
    title: title,
    description: description,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/terms-of-service`,
    }
  }
}

export default function CurrentPage() {
  return (
    <div className="page-container">
      <div className="mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">Terms of Service</h1>

        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 mb-6">
            Please read these terms carefully before using DevUtils's free Unix Timestamp Generator to generate <span className="font-bold">timestamp codes</span>.
          </p>

          <p className="text-sm text-gray-500 mb-8">
            Last Updated: August 9, 2025
          </p>

          <div className="space-y-8">
            <section>
              <p className="mb-4">
                Welcome to DevUtils, an independent tool for enhancing <span className="font-bold">developer productivity</span>. By accessing or using our Unix Timestamp Generator, you agree to be bound by these Terms of Service.
              </p>
              <p className="mb-6">
                If you disagree with any part of these terms, you may not use the Unix Timestamp Generator. DevUtils is an independent toolset, built by developers, for developers.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">1. Acceptance of Terms</h2>
              <p className="mb-4">
                By using DevUtils's Unix Timestamp Generator, you agree to comply with these Terms of Service and all applicable laws. If you do not agree, you are prohibited from accessing or using the Unix Timestamp Generator to create <span className="font-bold">timestamp codes</span>.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">2. Use License</h2>
              <p className="mb-4">
                DevUtils grants you a limited, non-exclusive license to use the Unix Timestamp Generator to create and copy <span className="font-bold">timestamp codes</span> for your personal and commercial projects. This license governs your use of the tool and its materials, and does not permit:
              </p>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>Modifying or copying the Unix Timestamp Generator’s materials or software</li>
                <li>Using the tool itself for direct commercial resale or public display</li>
                <li>Attempting to decompile or reverse engineer the Unix Timestamp Generator</li>
                <li>Removing any proprietary notices from the tool's interface or materials</li>
                <li>Transferring or mirroring Unix Timestamp Generator materials on other servers</li>
              </ul>
              <p className="mb-4">
                This license terminates automatically if you violate these terms. Upon termination, you must cease all use of the Unix Timestamp Generator and its materials.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">3. Disclaimer</h2>
              <p className="mb-4">
                DevUtils's Unix Timestamp Generator is provided “as is” with no warranties, express or implied, including warranties of merchantability, fitness for a particular purpose, or non-infringement of intellectual property related to <span className="font-bold">development tasks</span>.
              </p>
              <p className="mb-4">
                DevUtils does not guarantee the accuracy, reliability, or results of using the Unix Timestamp Generator to create <span className="font-bold">timestamp codes</span> or any linked content.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">4. Limitations</h2>
              <p className="mb-4">
                DevUtils is not liable for any damages (e.g., data loss, loss of profits) arising from the use or inability to use the Unix Timestamp Generator, even if notified of potential damages.
              </p>
              <p className="mb-4">
                Some jurisdictions may not allow limitations on implied warranties or liability for consequential damages, so these limitations may not apply to you.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">5. Accuracy of Materials</h2>
              <p className="mb-4">
                The Unix Timestamp Generator’s materials may contain technical or typographical errors. DevUtils does not warrant that <span className="font-bold">generated codes</span> or other content are accurate, complete, or current, and may update materials without notice.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">6. Links</h2>
              <p className="mb-4">
                DevUtils has not reviewed all sites linked from our website (e.g., devutils.app) and is not responsible for their content. Linking does not imply endorsement, and use of linked sites is at your own risk when using the Unix Timestamp Generator.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">7. Modifications</h2>
              <p className="mb-4">
                DevUtils may revise these Terms of Service at any time without notice. By using the Unix Timestamp Generator, you agree to be bound by the current version of these terms.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">8. Governing Law</h2>
              <p className="mb-4">
                These Terms of Service are governed by the laws of the United States, and you submit to the exclusive jurisdiction of its courts when using DevUtils's Unix Timestamp Generator.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">9. Intellectual Property</h2>
              <p className="mb-4">
                DevUtils respects intellectual property rights and expects users to do the same. The design, branding, and software of the Unix Timestamp Generator are the property of DevUtils.
              </p>
              <p className="mb-4">If you believe your copyrighted work has been infringed upon on our website, please provide:</p>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>A signature of the copyright owner or authorized representative</li>
                <li>Identification of the copyrighted work claimed to be infringed</li>
                <li>Details of the allegedly infringing material on our website</li>
                <li>Your contact information</li>
                <li>A good faith statement that the material’s use is unauthorized</li>
                <li>A statement verifying the accuracy of your notification</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">10. User Responsibilities</h2>
              <p className="mb-4">When using DevUtils's Unix Timestamp Generator, you agree to:</p>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>Use the Unix Timestamp Generator only for lawful purposes</li>
                <li>Respect the intellectual property rights of the DevUtils tool itself</li>
                <li>Not use the generated <span className="font-bold">timestamp codes</span> for any unlawful activities</li>
                <li>Not use the Unix Timestamp Generator for illegal or unauthorized purposes</li>
                <li>Not disrupt the Unix Timestamp Generator’s functionality or servers</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">11. Service Availability</h2>
              <p className="mb-4">
                DevUtils strives to keep the Unix Timestamp Generator available, but we do not guarantee uninterrupted access. We may modify, suspend, or discontinue the Unix Timestamp Generator at any time without notice.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">12. Contact Us</h2>
              <p className="mb-4">Have questions about these Terms of Service or the Unix Timestamp Generator? Contact us:</p>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>By email: <a href="mailto:<EMAIL>" className="text-blue-600"><EMAIL></a></li>
                <li>By visiting the contact section on our website</li>
              </ul>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
