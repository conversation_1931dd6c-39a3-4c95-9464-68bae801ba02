export async function generateMetadata({ params }) {
  const title = 'About Us';
  const description = 'We are a dynamic team of developers driven by a passion to make social media content accessible to all.';

  return {
    title: title,
    description: description,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/about-us`,
    },
  }
}

export default function CurrentPage() {
  return (
    <div className="page-container">
      {/* Hero Section */}
      <div className="section py-16">
        <h1 className="text-4xl font-bold mb-6">About Unix Timestamp Converter</h1>
        <p className="text-xl w-full mx-auto leading-relaxed">
          We’re a passionate team of developers behind DevUtils's free Unix Timestamp Generator, dedicated to empowering fellow developers and creators with seamless, powerful tools.
        </p>

      </div>

      <div className="section">
        <div className="w-full mx-auto">
          <p className="text-lg leading-relaxed mb-6">
            <PERSON><PERSON><PERSON><PERSON> was born in 2025 from a simple need: a better, faster way to handle common development tasks. Starting as a creative passion project to perfect the Unix Timestamp Generator, it has grown into a global tool trusted by thousands for enhancing their development workflows.
          </p>
          <p className="text-lg leading-relaxed mb-6">
            Our goal is to deliver a seamless, intuitive experience with the Unix Timestamp Generator, allowing users to effortlessly convert dates to Unix timestamps (and back again) with precision down to the second of the Unix Epoch (January 1, 1970). We’re committed to fueling developer productivity without barriers.
          </p>
          <p className="text-lg leading-relaxed mb-6">
            What sets DevUtils apart is our focus on user freedom and privacy. The Unix Timestamp Generator is completely free, requires no registration, and avoids intrusive data collection, ensuring a pure, efficient creative experience.
          </p>
          <p className="text-lg leading-relaxed">
            As DevUtils evolves, we stay dedicated to enhancing our tools, with the Unix Timestamp Generator being the first of many utilities in the growing DevUtils suite. Our team works tirelessly to refine features, ensuring developers can build, debug, and share with tools that are both powerful and elegant. DevUtils is an independent toolset, built by developers, for developers.
          </p>
        </div>
      </div>

      {/* Mission & Vision */}
      <div className="section mb-12">
        <div className="w-full mx-auto flex justify-between gap-8">
          <div className="bg-foreground/5 rounded-lg p-8">
            <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
            <p className="text-lg leading-relaxed">
              To empower developers with a free Unix Timestamp Generator for creating accurate timestamps, prioritizing ease, privacy, and a utility-first design.
            </p>
          </div>
          <div className="bg-foreground/5 rounded-lg p-8">
            <h2 className="text-2xl font-bold mb-4">Our Vision</h2>
            <p className="text-lg leading-relaxed">
              To inspire a global wave of developer productivity with the DevUtils toolset, enabling developers to craft and debug with tools that capture the spirit of efficiency and simplicity.
            </p>
          </div>
        </div>
      </div>

      {/* Our Impact */}
      <div className="section mb-12">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-4xl font-bold text-blue-600 mb-2">1M+</div>
            <div className="text-lg text-gray-600">Conversions</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-green-600 mb-2">100+</div>
            <div className="text-lg text-gray-600">Countries</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-purple-600 mb-2">99.9%</div>
            <div className="text-lg text-gray-600">Uptime</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-lg text-gray-600">Support</div>
          </div>
        </div>
      </div>

      {/* Contact Us */}
      <div className="section">
        <div className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Contact Us</h2>
        </div>
        <p className="text-lg leading-relaxed">
          Have questions about DevUtils's Unix Timestamp Generator? Reach out at  <a href="mailto:<EMAIL>" className="text-blue-600"><EMAIL></a>.
        </p>
      </div>
    </div>
  );
}
