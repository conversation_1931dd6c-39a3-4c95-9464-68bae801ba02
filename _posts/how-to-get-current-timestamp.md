---
title: 'How to Get Current Timestamp?'
date: '2025-08-10'
author: 'Devutils Team'
excerpt: "Learn how to get current Unix timestamps (seconds or milliseconds) in multiple programming languages, validate with DevUtils!"
tags: ["tutorial", "Unix Timestamp", "Unix Timestamp Converter"]
---

## Swift
```swift
NSDate().timeIntervalSince1970
```

## Go
```go
import (
  "time"
)
int64(time.Now().Unix())
```

## Java
```java
System.currentTimeMillis() / 1000
```

## C
```c
#include <sys/time.h>

// ...
struct timeval tv;
gettimeofday(&tv, NULL);
// Second： tv.tv_sec
// Millisecond： tv.tv_sec * 1000LL + tv.tv_usec / 1000
```

## JavaScript
```javascript
Math.round(new Date() / 1000)
```

## Objective-C
```objc
[[NSDate date] timeIntervalSince1970]
```

## MySQL
```sql
SELECT unix_timestamp(now())
```

## SQLite
```sql
SELECT strftime('%s', 'now')
```

## Erlang
```erlang
calendar:datetime_to_gregorian_seconds(calendar:universal_time())-719528*24*3600.
```

## PHP
```php
<?php
// pure php
time();
```

## Python
```python
import time
time.time()
```

## Ruby
```ruby
Time.now.to_i
```

## Shell
```bash
date +%s
```

## Groovy
```groovy
(new Date().time / 1000).longValue()
```

## Lua
```lua
os.time()
```

## .NET/C#
```csharp
DateTimeOffset.UtcNow.ToUnixTimeSeconds();
```

## Dart
```dart
(new DateTime.now().millisecondsSinceEpoch / 1000).truncate()
```
