---
title: '3 Ways to Get a Unix Timestamp in Shell (Seconds & Milliseconds)'
date: '2025-08-10'
author: 'Devutils Team'
excerpt: "Need a Unix timestamp in your shell script? Learn 3 easy methods using the date command and more to get timestamps in seconds or milliseconds on any system."
tags: ["tutorial", "Unix Timestamp", "Unix Timestamp Converter"]
---

In shell scripting and system automation, the **Unix timestamp** is the universal standard for logging events, naming files, or tracking time. It provides a simple, numeric representation of a point in time, making it incredibly useful for a wide range of tasks.

This guide covers three practical methods to get the current **Unix timestamp** directly from your command line, in both seconds and high-precision milliseconds. We'll also show you how to use a **Unix timestamp converter** to easily verify your results.

***

## What Is a Unix Timestamp?

A **Unix timestamp** is the total number of seconds that have passed since the Unix epoch: **January 1, 1970, at 00:00:00 UTC**. Because it's a single integer, it's free from timezone complexities and easy for scripts to process.

* **Seconds Timestamp (10-digit):** The most common format (e.g., `1754896707`).
* **Milliseconds Timestamp (13-digit):** Used for high-precision tasks (e.g., `1754896707123`).

***

## Method 1: Using the `date` Command (The Standard Way)

The `date` command is the most common and direct way to get a **Unix timestamp**.

### Get Timestamp in Seconds
To get the standard 10-digit timestamp, use the `+%s` format specifier.

```sh
date +%s
# Example Output: 1754896707
```

### Get Timestamp in Milliseconds
On most modern Linux systems (using GNU `date`), you can combine `%s` with `%N` (nanoseconds) to get millisecond precision. We take the first 3 digits of the nanoseconds (`%3N`).

```sh
date +%s%3N
# Example Output: 1754896707123
```

## Method 2: Alternative Commands for Compatibility (e.g., macOS)
The `%N` specifier is not available in all versions of `date`, particularly on macOS and other BSD-based systems. Here are compatible alternatives to get a millisecond Unix timestamp.

### Using `gdate` on macOS
If you use Homebrew on a Mac, you can install `coreutils` to get the GNU version of date, typically called `gdate`.

```sh
# First, install coreutils: brew install coreutils
gdate +%s%3N
# Example Output: 1754896707123
```

### Using Python or Perl
Most systems have Python or Perl installed, which can also provide a millisecond timestamp.

```sh
# Using Python
python3 -c 'import time; print(int(time.time() * 1000))'

# Using Perl
perl -MTime::HiRes -e 'printf("%.0f\n", Time::HiRes::time() * 1000)'
```

## Method 3: Using a Unix Timestamp Converter for Verification
When writing scripts, it's essential to verify that your timestamp values are correct. A dedicated online Unix timestamp converter is the perfect tool for this, saving you time from running commands repeatedly.

The DevUtils [Unix Timestamp Converter](https://timestamps.top) is a free tool that helps you:

- Instantly paste a Unix timestamp generated from your shell to see its human-readable date.

- Quickly convert any date to a timestamp to get the correct integer for your scripts.

- Generate timestamps in other useful formats, like for Discord (`<t:1754896707:F>`).

- Work securely, as all conversions happen in your browser.

Whether you're using the standard `date` command or a more compatible alternative, these methods make it easy to work with any Unix timestamp in your shell scripts. Always use a reliable [Unix timestamp converter](https://timestamps.top) like DevUtils to validate your results and streamline your workflow.
