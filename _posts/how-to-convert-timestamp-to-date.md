---
title: 'Timestamp to Date'
date: '2025-08-10'
author: 'Devutils Team'
excerpt: "Learn how to convert Unix timestamp to datetime in multiple programming languages, validate with DevUtils!"
tags: ["tutorial", "Unix Timestamp", "Unix Timestamp Converter", "sql"]
---

## Swift
```swift
let timestamp = 1624713600.0
let date = NSDate(timeIntervalSince1970: timestamp)
```

## Go
```go
import (
    "time"
)
timestamp := int64(1624713600)
t := time.Unix(timestamp, 0)
```

## Java
```java
long timestamp = 1624713600;
java.util.Date date = new java.util.Date(timestamp * 1000);
```

## C
```c
#include <time.h>
time_t timestamp = 1624713600;
struct tm *timeinfo = localtime(&timestamp);
```

## JavaScript
```javascript
const timestamp = 1624713600;
const date = new Date(timestamp * 1000);
```

## Objective-C
```objc
NSTimeInterval timestamp = 1624713600;
NSDate *date = [NSDate dateWithTimeIntervalSince1970:timestamp];
```

## MySQL
```sql
SELECT from_unixtime(1624713600)
```

## SQLite
```sql
SELECT datetime(1624713600, 'unixepoch')
```

## Erlang
```erlang
Timestamp = 1624713600,
Datetime = calendar:gregorian_seconds_to_datetime(Timestamp + 719528*24*3600).
```

## PHP
```php
<?php
// pure php
$timestamp = 1624713600;
$date = date('Y-m-d H:i:s', $timestamp);
```

## Python
```python
from datetime import datetime
timestamp = 1624713600
date = datetime.fromtimestamp(timestamp)
```

## Ruby
```ruby
timestamp = 1624713600
time = Time.at(timestamp)
```

## Shell
```bash
date -d @1624713600
# macOS: date -r 1624713600
```

## Groovy
```groovy
long timestamp = 1624713600
Date date = new Date(timestamp * 1000)
```

## Lua
```lua
timestamp = 1624713600
date = os.date("*t", timestamp)
```

## .NET/C#
```csharp
long timestamp = 1624713600;
DateTimeOffset date = DateTimeOffset.FromUnixTimeSeconds(timestamp);
```

## Dart
```dart
int timestamp = 1624713600;
DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
```
