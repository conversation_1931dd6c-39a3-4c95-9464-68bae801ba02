---
title: 'Date to Timestamp'
date: '2025-08-10'
author: 'Devutils Team'
excerpt: "Learn how to convert datetime to Unix timestamp in multiple programming languages, validate with DevUtils!"
tags: ["tutorial", "Unix Timestamp", "Unix Timestamp Converter", "sql"]
---

## Swift
```swift
let date = NSDate()
let timestamp = date.timeIntervalSince1970
```

## Go
```go
import (
    "time"
)
t := time.Now()
timestamp := t.Unix()
```

## Java
```java
java.util.Date date = new java.util.Date();
long timestamp = date.getTime() / 1000;
```

## C
```c
#include <time.h>
time_t t = time(NULL);
long timestamp = (long)t;
```

## JavaScript
```javascript
const date = new Date();
const timestamp = Math.round(date.getTime() / 1000);
```

## Objective-C
```objc
NSDate *date = [NSDate date];
NSTimeInterval timestamp = [date timeIntervalSince1970];
```

## MySQL
```sql
SELECT unix_timestamp('2021-06-26 12:00:00')
```

## SQLite
```sql
SELECT strftime('%s', '2021-06-26 12:00:00')
```

## Erlang
```erlang
Datetime = calendar:universal_time(),
Timestamp = calendar:datetime_to_gregorian_seconds(Datetime) - 719528*24*3600.
```

## PHP
```php
<?php
// pure php
$date = new DateTime();
$timestamp = $date->getTimestamp();
```

## Python
```python
import time
from datetime import datetime
date = datetime.now()
timestamp = int(date.timestamp())
```

## Ruby
```ruby
time = Time.now
timestamp = time.to_i
```

## Shell
```bash
time = Time.now
timestamp = time.to_i
```

## Groovy
```groovy
Date date = new Date()
long timestamp = (date.time / 1000).longValue()
```

## Lua
```lua
date = os.time({year=2021, month=6, day=26, hour=12, min=0, sec=0})
timestamp = date
```

## .NET/C#
```csharp
DateTimeOffset date = DateTimeOffset.Now;
long timestamp = date.ToUnixTimeSeconds();
```

## Dart
```dart
DateTimeOffset date = DateTimeOffset.Now;
long timestamp = date.ToUnixTimeSeconds();
```
